{ static SET: ::phf::Set<&'static str> = ::phf::Set { map: ::phf::Map {
    key: 732231254413039614,
    disps: ::phf::Slice::Static(&[
        (2, 10),
        (1, 0),
        (3, 12),
        (2, 12),
        (37, 1),
        (2, 4),
        (5, 5),
        (11, 9),
        (1, 0),
        (0, 22),
    ]),
    entries: ::phf::Slice::Static(&[
        ("nowrap", ()),
        ("noresize", ()),
        ("target", ()),
        ("alink", ()),
        ("media", ()),
        ("dir", ()),
        ("disabled", ()),
        ("rev", ()),
        ("align", ()),
        ("hreflang", ()),
        ("compact", ()),
        ("lang", ()),
        ("readonly", ()),
        ("selected", ()),
        ("color", ()),
        ("frame", ()),
        ("vlink", ()),
        ("link", ()),
        ("valign", ()),
        ("direction", ()),
        ("axis", ()),
        ("charset", ()),
        ("rel", ()),
        ("method", ()),
        ("language", ()),
        ("http-equiv", ()),
        ("shape", ()),
        ("bgcolor", ()),
        ("clear", ()),
        ("text", ()),
        ("noshade", ()),
        ("multiple", ()),
        ("checked", ()),
        ("accept", ()),
        ("nohref", ()),
        ("codetype", ()),
        ("scope", ()),
        ("valuetype", ()),
        ("type", ()),
        ("accept-charset", ()),
        ("face", ()),
        ("rules", ()),
        ("enctype", ()),
        ("scrolling", ()),
        ("defer", ()),
        ("declare", ()),
    ]),
} }; &SET }