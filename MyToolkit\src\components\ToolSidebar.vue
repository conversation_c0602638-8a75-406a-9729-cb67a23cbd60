<template>
  <div class="tool-sidebar">
    <!-- 搜索框 -->
    <div class="search-section">
      <Input
        v-model:value="searchKeyword"
        placeholder="搜索工具..."
        allow-clear
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </Input>
    </div>
    
    <!-- 工具分类列表 -->
    <div class="tool-categories">
      <div
        v-for="(tools, category) in filteredToolsByCategory"
        :key="category"
        class="category-section"
      >
        <div class="category-title">
          {{ getCategoryName(category) }}
        </div>
        
        <div class="tool-list">
          <div
            v-for="tool in tools"
            :key="tool.id"
            class="tool-item"
            :class="{ active: appStore.activeTab === tool.id }"
            @click="handleToolClick(tool)"
          >
            <div class="tool-icon">
              <component :is="getIconComponent(tool.icon)" />
            </div>
            
            <div class="tool-info">
              <div class="tool-name">{{ tool.name }}</div>
              <div class="tool-desc">{{ tool.description }}</div>
            </div>
            
            <div v-if="tool.id === 'qr-decoder'" class="tool-badge">
              NEW
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部信息 -->
    <div class="sidebar-footer">
      <div class="footer-info">
        {{ appStore.appName }} v{{ appStore.appVersion }}
      </div>
      <div class="footer-links">
        <a href="#" @click.prevent="showHelp">帮助</a>
        <a href="#" @click.prevent="showSettings">设置</a>
        <a href="#" @click.prevent="showAbout">关于</a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Input } from 'ant-design-vue'
import {
  SearchOutlined,
  QrcodeOutlined,
  PlusSquareOutlined,
  CodeOutlined,
  FileOutlined,
  SettingOutlined,
  ToolOutlined
} from '@ant-design/icons-vue'
import { useAppStore } from '@/stores/app'
import { useRouter } from 'vue-router'
import type { Tool } from '@/types'

const appStore = useAppStore()
const router = useRouter()

const searchKeyword = ref('')

// 计算过滤后的工具列表
const filteredToolsByCategory = computed(() => {
  if (!searchKeyword.value) {
    return appStore.toolsByCategory
  }
  
  const filtered: Record<string, Tool[]> = {}
  const keyword = searchKeyword.value.toLowerCase()
  
  Object.entries(appStore.toolsByCategory).forEach(([category, tools]) => {
    const matchedTools = tools.filter(tool =>
      tool.name.toLowerCase().includes(keyword) ||
      tool.description.toLowerCase().includes(keyword)
    )
    
    if (matchedTools.length > 0) {
      filtered[category] = matchedTools
    }
  })
  
  return filtered
})

// 获取分类名称
const getCategoryName = (category: string) => {
  const categoryNames: Record<string, string> = {
    image: '图像处理',
    text: '文本处理',
    file: '文件工具',
    network: '网络工具',
    system: '系统工具',
    other: '其他工具',
  }
  return categoryNames[category] || category
}

// 获取图标组件
const getIconComponent = (icon: string) => {
  const iconMap: Record<string, any> = {
    qrcode: QrcodeOutlined,
    'plus-square': PlusSquareOutlined,
    code: CodeOutlined,
    file: FileOutlined,
    setting: SettingOutlined,
  }
  return iconMap[icon] || ToolOutlined
}

// 处理工具点击
const handleToolClick = (tool: Tool) => {
  if (!tool.enabled) {
    return
  }
  
  // 打开工具
  appStore.openTool(tool.id)
  
  // 路由跳转
  router.push(tool.route)
}

// 显示帮助
const showHelp = () => {
  // TODO: 实现帮助功能
  console.log('显示帮助')
}

// 显示设置
const showSettings = () => {
  router.push('/settings')
}

// 显示关于
const showAbout = () => {
  // TODO: 实现关于对话框
  console.log('显示关于')
}
</script>

<style lang="less" scoped>
.tool-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

.search-section {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  background: white;
}

.tool-categories {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.category-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.category-title {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 8px;
}

.tool-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  position: relative;
  
  &:hover {
    background: white;
    border-color: #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }
  
  &.active {
    background: #eff6ff;
    border-color: #3b82f6;
    color: #3b82f6;
  }
}

.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tool-info {
  flex: 1;
}

.tool-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
}

.tool-desc {
  font-size: 12px;
  color: #64748b;
  line-height: 1.3;
}

.tool-badge {
  background: #10b981;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  background: white;
}

.footer-info {
  text-align: center;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 16px;
  
  a {
    color: #64748b;
    text-decoration: none;
    font-size: 12px;
    transition: color 0.2s;
    
    &:hover {
      color: #3b82f6;
    }
  }
}

/* 滚动条样式 */
.tool-categories::-webkit-scrollbar {
  width: 6px;
}

.tool-categories::-webkit-scrollbar-track {
  background: transparent;
}

.tool-categories::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tool-categories::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
