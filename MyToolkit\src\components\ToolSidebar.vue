<template>
  <div class="tool-sidebar">
    <!-- 搜索框 -->
    <div class="search-section">
      <Input
        v-model:value="searchKeyword"
        placeholder="搜索工具..."
        allow-clear
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </Input>
    </div>
    
    <!-- 工具分类列表 -->
    <div class="tool-categories">
      <div
        v-for="(tools, category) in filteredToolsByCategory"
        :key="category"
        class="category-section"
      >
        <div class="category-title">
          {{ getCategoryName(category) }}
        </div>
        
        <div class="tool-list">
          <div
            v-for="tool in tools"
            :key="tool.id"
            class="tool-item"
            :class="{ active: appStore.activeTab === tool.id }"
            @click="handleToolClick(tool)"
          >
            <div class="tool-icon">
              <component :is="getIconComponent(tool.icon)" />
            </div>
            
            <div class="tool-info">
              <div class="tool-name">{{ tool.name }}</div>
              <div class="tool-desc">{{ tool.description }}</div>
            </div>
            
            <div v-if="tool.id === 'qr-decoder'" class="tool-badge">
              NEW
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部信息 -->
    <div class="sidebar-footer">
      <div class="footer-info">
        {{ appStore.appName }} v{{ appStore.appVersion }}
      </div>
      <div class="footer-links">
        <a href="#" @click.prevent="showHelp">帮助</a>
        <a href="#" @click.prevent="showSettings">设置</a>
        <a href="#" @click.prevent="showAbout">关于</a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Input } from 'ant-design-vue'
import {
  SearchOutlined,
  QrcodeOutlined,
  PlusSquareOutlined,
  CodeOutlined,
  FileOutlined,
  SettingOutlined,
  ToolOutlined
} from '@ant-design/icons-vue'
import { useAppStore } from '@/stores/app'
import { useRouter } from 'vue-router'
import type { Tool } from '@/types'

const appStore = useAppStore()
const router = useRouter()

const searchKeyword = ref('')

// 计算过滤后的工具列表
const filteredToolsByCategory = computed(() => {
  if (!searchKeyword.value) {
    return appStore.toolsByCategory
  }
  
  const filtered: Record<string, Tool[]> = {}
  const keyword = searchKeyword.value.toLowerCase()
  
  Object.entries(appStore.toolsByCategory).forEach(([category, tools]) => {
    const matchedTools = tools.filter(tool =>
      tool.name.toLowerCase().includes(keyword) ||
      tool.description.toLowerCase().includes(keyword)
    )
    
    if (matchedTools.length > 0) {
      filtered[category] = matchedTools
    }
  })
  
  return filtered
})

// 获取分类名称
const getCategoryName = (category: string) => {
  const categoryNames: Record<string, string> = {
    image: '图像处理',
    text: '文本处理',
    file: '文件工具',
    network: '网络工具',
    system: '系统工具',
    other: '其他工具',
  }
  return categoryNames[category] || category
}

// 获取图标组件
const getIconComponent = (icon: string) => {
  const iconMap: Record<string, any> = {
    qrcode: QrcodeOutlined,
    'plus-square': PlusSquareOutlined,
    code: CodeOutlined,
    file: FileOutlined,
    setting: SettingOutlined,
  }
  return iconMap[icon] || ToolOutlined
}

// 处理工具点击
const handleToolClick = (tool: Tool) => {
  if (!tool.enabled) {
    return
  }
  
  // 打开工具
  appStore.openTool(tool.id)
  
  // 路由跳转
  router.push(tool.route)
}

// 显示帮助
const showHelp = () => {
  // TODO: 实现帮助功能
  console.log('显示帮助')
}

// 显示设置
const showSettings = () => {
  router.push('/settings')
}

// 显示关于
const showAbout = () => {
  // TODO: 实现关于对话框
  console.log('显示关于')
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/variables.less';

.tool-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: @background-color-light;
}

.search-section {
  padding: @spacing-lg;
  border-bottom: 1px solid @border-color;
  background: @background-color;

  :deep(.ant-input-affix-wrapper) {
    border-radius: @border-radius-md;
    transition: all @transition-normal;

    &:hover {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }

    &:focus-within {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
  }
}

.tool-categories {
  flex: 1;
  overflow-y: auto;
  padding: @spacing-lg;
}

.category-section {
  margin-bottom: @spacing-xxl;

  &:last-child {
    margin-bottom: 0;
  }
}

.category-title {
  font-size: @font-size-xs;
  font-weight: 600;
  color: @text-color-secondary;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: @spacing-sm;
  padding: 0 @spacing-sm;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: @spacing-sm;
    width: 20px;
    height: 2px;
    background: @gradient-primary;
    border-radius: 1px;
  }
}

.tool-list {
  display: flex;
  flex-direction: column;
  gap: @spacing-xs;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: @spacing-md;
  border-radius: @border-radius-md;
  cursor: pointer;
  transition: all @transition-normal;
  border: 1px solid transparent;
  position: relative;

  &:hover {
    background: @background-color;
    border-color: @border-color;
    box-shadow: @box-shadow-md;
    transform: translateY(-1px);

    .tool-icon {
      transform: scale(1.05);
    }
  }

  &.active {
    background: rgba(59, 130, 246, 0.1);
    border-color: @primary-color;
    color: @primary-color;

    .tool-icon {
      background: @gradient-primary;
      box-shadow: @box-shadow-sm;
    }

    .tool-name {
      color: @primary-color;
      font-weight: 600;
    }
  }

  &:not(.active) .tool-icon {
    background: @gradient-primary;
  }
}

.tool-icon {
  width: 40px;
  height: 40px;
  border-radius: @border-radius-md;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: @spacing-md;
  font-size: @font-size-lg;
  background: @gradient-primary;
  color: white;
  transition: all @transition-normal;
  box-shadow: @box-shadow-sm;
}

.tool-info {
  flex: 1;
  min-width: 0; // 防止文本溢出
}

.tool-name {
  font-weight: 500;
  font-size: @font-size-sm;
  margin-bottom: 2px;
  transition: color @transition-fast;
}

.tool-desc {
  font-size: @font-size-xs;
  color: @text-color-secondary;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tool-badge {
  background: @success-color;
  color: white;
  font-size: 10px;
  padding: 2px @spacing-xs;
  border-radius: @border-radius-sm;
  font-weight: 500;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.sidebar-footer {
  padding: @spacing-lg;
  border-top: 1px solid @border-color;
  background: @background-color;
}

.footer-info {
  text-align: center;
  font-size: @font-size-xs;
  color: @text-color-secondary;
  margin-bottom: @spacing-sm;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: @spacing-lg;

  a {
    color: @text-color-secondary;
    text-decoration: none;
    font-size: @font-size-xs;
    transition: color @transition-normal;
    padding: @spacing-xs;
    border-radius: @border-radius-sm;

    &:hover {
      color: @primary-color;
      background: rgba(59, 130, 246, 0.1);
    }
  }
}

/* 滚动条样式 */
.tool-categories::-webkit-scrollbar {
  width: 6px;
}

.tool-categories::-webkit-scrollbar-track {
  background: transparent;
}

.tool-categories::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tool-categories::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

// 响应式适配
@media (max-width: 768px) {
  .tool-sidebar {
    .search-section {
      padding: @spacing-md;
    }

    .tool-categories {
      padding: @spacing-md;
    }

    .tool-item {
      padding: @spacing-sm;

      .tool-icon {
        width: 32px;
        height: 32px;
        font-size: @font-size-md;
        margin-right: @spacing-sm;
      }

      .tool-name {
        font-size: @font-size-xs;
      }

      .tool-desc {
        font-size: 10px;
      }
    }
  }
}
</style>
