D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\deps\libmarkup5ever-a2dac4ae53a6cd10.rmeta: D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\lib.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\data\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\tree_builder.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\serialize.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\buffer_queue.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\smallcharset.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/generated.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/named_entities.rs

D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\deps\libmarkup5ever-a2dac4ae53a6cd10.rlib: D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\lib.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\data\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\tree_builder.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\serialize.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\buffer_queue.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\smallcharset.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/generated.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/named_entities.rs

D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\deps\markup5ever-a2dac4ae53a6cd10.d: D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\lib.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\data\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\mod.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\tree_builder.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\serialize.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\buffer_queue.rs D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\smallcharset.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/generated.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/named_entities.rs

D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\lib.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\data\mod.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\mod.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\interface\tree_builder.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\serialize.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\buffer_queue.rs:
D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\markup5ever-0.11.0\util\smallcharset.rs:
D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/generated.rs:
D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\markup5ever-88bec1784f87a086\out/named_entities.rs:

# env-dep:OUT_DIR=D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\markup5ever-88bec1784f87a086\\out
