<template>
  <div class="home-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <ToolSidebar />
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- Tab栏 -->
      <div class="tab-bar" v-if="appStore.openTabs.length > 0">
        <Tabs
          v-model:activeKey="appStore.activeTab"
          type="editable-card"
          @edit="handleTabEdit"
          @tabClick="handleTabClick"
        >
          <TabPane
            v-for="toolId in appStore.openTabs"
            :key="toolId"
            :tab="getToolName(toolId)"
            :closable="true"
          />
        </Tabs>
      </div>
      
      <!-- 工具内容区 -->
      <div class="tool-content">
        <router-view v-if="appStore.activeTab" />
        <EmptyState v-else />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tabs, TabPane } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import ToolSidebar from '@/components/ToolSidebar.vue'
import EmptyState from '@/components/EmptyState.vue'

const appStore = useAppStore()
const router = useRouter()

const getToolName = (toolId: string) => {
  const tool = appStore.getToolById(toolId)
  return tool?.name || toolId
}

const handleTabEdit = (targetKey: string, action: 'add' | 'remove') => {
  if (action === 'remove') {
    appStore.closeTool(targetKey)
  }
}

const handleTabClick = (key: string) => {
  appStore.switchTab(key)

  // 路由跳转
  const tool = appStore.getToolById(key)
  if (tool) {
    router.push(tool.route)
  }
}
</script>

<style lang="less" scoped>
.home-container {
  height: 100%;
  display: flex;
  background: #ffffff;
}

.sidebar {
  width: 280px;
  border-right: 1px solid #e2e8f0;
  background: #f8fafc;
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-bar {
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 16px;
  
  :deep(.ant-tabs) {
    margin: 0;
    
    .ant-tabs-nav {
      margin: 0;
      
      &::before {
        border: none;
      }
    }
    
    .ant-tabs-tab {
      background: white;
      border: 1px solid #e2e8f0;
      border-bottom: none;
      border-radius: 8px 8px 0 0;
      margin-right: 4px;
      
      &.ant-tabs-tab-active {
        background: white;
        border-color: #3b82f6;
        
        .ant-tabs-tab-btn {
          color: #3b82f6;
        }
      }
    }
  }
}

.tool-content {
  flex: 1;
  overflow: hidden;
  background: white;
}
</style>
