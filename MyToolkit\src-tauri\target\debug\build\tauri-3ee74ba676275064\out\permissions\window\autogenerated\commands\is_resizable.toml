# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-resizable"
description = "Enables the is_resizable command without any pre-configured scope."
commands.allow = ["is_resizable"]

[[permission]]
identifier = "deny-is-resizable"
description = "Denies the is_resizable command without any pre-configured scope."
commands.deny = ["is_resizable"]
