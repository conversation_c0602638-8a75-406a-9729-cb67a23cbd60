<template>
  <div class="title-bar" data-tauri-drag-region>
    <!-- 窗口控制按钮 -->
    <div class="window-controls">
      <div class="control-btn close" @click="handleClose"></div>
      <div class="control-btn minimize" @click="handleMinimize"></div>
      <div class="control-btn maximize" @click="handleMaximize"></div>
    </div>
    
    <!-- 应用标题 -->
    <div class="app-title">
      <ToolOutlined class="app-icon" />
      <span class="title-text">{{ appStore.appName || 'MyToolkit' }}</span>
    </div>
    
    <!-- 版本信息 -->
    <div class="app-version">
      v{{ appStore.appVersion }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ToolOutlined } from '@ant-design/icons-vue'
import { useAppStore } from '@/stores/app'
// getCurrentWebviewWindow is auto-imported via vite.config.ts

const appStore = useAppStore()

const handleClose = async () => {
  const appWindow = getCurrentWebviewWindow()
  await appWindow.close()
}

const handleMinimize = async () => {
  const appWindow = getCurrentWebviewWindow()
  await appWindow.minimize()
}

const handleMaximize = async () => {
  const appWindow = getCurrentWebviewWindow()
  await appWindow.toggleMaximize()
}
</script>

<style lang="less" scoped>
.title-bar {
  height: 40px;
  background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  user-select: none;
  position: relative;
}

.window-controls {
  display: flex;
  gap: 8px;
  z-index: 10;
}

.control-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  
  &.close {
    background: #ff5f57;
    
    &:hover {
      background: #ff3b30;
    }
  }
  
  &.minimize {
    background: #ffbd2e;
    
    &:hover {
      background: #ff9500;
    }
  }
  
  &.maximize {
    background: #28ca42;
    
    &:hover {
      background: #30d158;
    }
  }
}

.app-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
}

.app-icon {
  font-size: 16px;
  color: #3b82f6;
}

.title-text {
  font-size: 14px;
}

.app-version {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}
</style>
