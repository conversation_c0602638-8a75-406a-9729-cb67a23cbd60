# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-basename"
description = "Enables the basename command without any pre-configured scope."
commands.allow = ["basename"]

[[permission]]
identifier = "deny-basename"
description = "Denies the basename command without any pre-configured scope."
commands.deny = ["basename"]
