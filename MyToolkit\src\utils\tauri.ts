/**
 * Tauri工具函数
 */

// 检查是否在Tauri环境中
export function isTauriEnvironment(): boolean {
  return typeof window !== 'undefined' && '__TAURI__' in window
}

// 安全的invoke函数
async function safeInvoke(command: string, args?: any): Promise<any> {
  if (!isTauriEnvironment()) {
    console.warn('不在Tauri环境中，跳过命令调用:', command)
    return null
  }

  try {
    const { invoke } = await import('@tauri-apps/api/core')
    return await invoke(command, args)
  } catch (error) {
    console.error(`Tauri命令调用失败 [${command}]:`, error)
    return null
  }
}

// 获取应用版本
export async function getAppVersion(): Promise<string> {
  try {
    const version = await safeInvoke('get_app_version')
    return version || '1.0.0'
  } catch (error) {
    console.warn('获取应用版本失败:', error)
    return '1.0.0'
  }
}

// 获取应用名称
export async function getAppName(): Promise<string> {
  try {
    const name = await safeInvoke('get_app_name')
    return name || 'MyToolkit'
  } catch (error) {
    console.warn('获取应用名称失败:', error)
    return 'MyToolkit'
  }
}

// 保存工具状态
export async function saveToolState(toolId: string, stateData: any): Promise<void> {
  if (!isTauriEnvironment()) {
    // 在Web环境中使用localStorage作为fallback
    try {
      localStorage.setItem(`tool_state_${toolId}`, JSON.stringify(stateData))
      return
    } catch (error) {
      console.warn('localStorage保存失败:', error)
      return
    }
  }

  try {
    await safeInvoke('save_tool_state', {
      toolId,
      stateData: JSON.stringify(stateData)
    })
  } catch (error) {
    console.error('保存工具状态失败:', error)
    // 不抛出错误，避免阻断应用流程
  }
}

// 加载工具状态
export async function loadToolState(toolId: string): Promise<any> {
  if (!isTauriEnvironment()) {
    // 在Web环境中使用localStorage作为fallback
    try {
      const data = localStorage.getItem(`tool_state_${toolId}`)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.warn('localStorage读取失败:', error)
      return null
    }
  }

  try {
    const result = await safeInvoke('load_tool_state', { toolId })
    return result ? JSON.parse(result as string) : null
  } catch (error) {
    console.warn('加载工具状态失败:', error)
    return null
  }
}

// 读取文件内容
export async function readFileContent(filePath: string): Promise<string> {
  if (!isTauriEnvironment()) {
    throw new Error('文件操作仅在Tauri环境中可用')
  }

  try {
    const result = await safeInvoke('read_file_content', { filePath })
    if (result === null) {
      throw new Error('读取文件失败')
    }
    return result
  } catch (error) {
    console.error('读取文件失败:', error)
    throw error
  }
}

// 写入文件内容
export async function writeFileContent(filePath: string, content: string): Promise<void> {
  if (!isTauriEnvironment()) {
    throw new Error('文件操作仅在Tauri环境中可用')
  }

  try {
    await safeInvoke('write_file_content', { filePath, content })
  } catch (error) {
    console.error('写入文件失败:', error)
    throw error
  }
}

// 获取文件信息
export async function getFileInfo(filePath: string): Promise<any> {
  if (!isTauriEnvironment()) {
    throw new Error('文件操作仅在Tauri环境中可用')
  }

  try {
    const result = await safeInvoke('get_file_info', { filePath })
    if (result === null) {
      throw new Error('获取文件信息失败')
    }
    return result
  } catch (error) {
    console.error('获取文件信息失败:', error)
    throw error
  }
}

// 安全调用Tauri命令（向后兼容）
export async function safeTauriInvoke<T>(command: string, args?: any): Promise<T | null> {
  return await safeInvoke(command, args)
}
