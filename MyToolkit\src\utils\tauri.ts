import { invoke } from '@tauri-apps/api/core'

/**
 * Tauri工具函数
 */

// 获取应用版本
export async function getAppVersion(): Promise<string> {
  try {
    return await invoke('get_app_version')
  } catch (error) {
    console.warn('获取应用版本失败:', error)
    return '1.0.0'
  }
}

// 获取应用名称
export async function getAppName(): Promise<string> {
  try {
    return await invoke('get_app_name')
  } catch (error) {
    console.warn('获取应用名称失败:', error)
    return 'MyToolkit'
  }
}

// 保存工具状态
export async function saveToolState(toolId: string, stateData: any): Promise<void> {
  try {
    await invoke('save_tool_state', {
      toolId,
      stateData: JSON.stringify(stateData)
    })
  } catch (error) {
    console.error('保存工具状态失败:', error)
    throw error
  }
}

// 加载工具状态
export async function loadToolState(toolId: string): Promise<any> {
  try {
    const result = await invoke('load_tool_state', { toolId })
    return result ? JSON.parse(result as string) : null
  } catch (error) {
    console.warn('加载工具状态失败:', error)
    return null
  }
}

// 读取文件内容
export async function readFileContent(filePath: string): Promise<string> {
  try {
    return await invoke('read_file_content', { filePath })
  } catch (error) {
    console.error('读取文件失败:', error)
    throw error
  }
}

// 写入文件内容
export async function writeFileContent(filePath: string, content: string): Promise<void> {
  try {
    await invoke('write_file_content', { filePath, content })
  } catch (error) {
    console.error('写入文件失败:', error)
    throw error
  }
}

// 获取文件信息
export async function getFileInfo(filePath: string): Promise<any> {
  try {
    return await invoke('get_file_info', { filePath })
  } catch (error) {
    console.error('获取文件信息失败:', error)
    throw error
  }
}

// 检查是否在Tauri环境中
export function isTauriEnvironment(): boolean {
  return typeof window !== 'undefined' && '__TAURI__' in window
}

// 安全调用Tauri命令
export async function safeTauriInvoke<T>(command: string, args?: any): Promise<T | null> {
  if (!isTauriEnvironment()) {
    console.warn('不在Tauri环境中，跳过命令调用:', command)
    return null
  }
  
  try {
    return await invoke(command, args)
  } catch (error) {
    console.error(`Tauri命令调用失败 [${command}]:`, error)
    return null
  }
}
