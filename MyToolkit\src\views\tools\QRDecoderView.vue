<template>
  <div class="qr-decoder-view">
    <div class="tool-header">
      <div class="tool-title">
        <QrcodeOutlined class="tool-icon" />
        <div class="tool-info">
          <h1>二维码解码工具</h1>
          <p>支持拖拽、点击选择和剪贴板粘贴二维码图片</p>
        </div>
      </div>

      <div class="tool-actions">
        <Button @click="clearAll">
          <DeleteOutlined />
          清空
        </Button>
        <Button @click="showHelp">
          <QuestionCircleOutlined />
          帮助
        </Button>
      </div>
    </div>
    
    <div class="tool-content">
      <div class="upload-section">
        <Card class="upload-card">
          <div class="upload-area" @click="selectFile">
            <CloudUploadOutlined class="upload-icon" />
            <h3>上传二维码图片</h3>
            <p>拖拽图片到此处，或点击选择文件<br>也可以使用 Ctrl+V 粘贴剪贴板中的图片</p>
            <div class="upload-buttons">
              <Button type="primary">
                <FolderOpenOutlined />
                选择文件
              </Button>
              <Button @click.stop="pasteFromClipboard">
                <CopyOutlined />
                从剪贴板粘贴
              </Button>
            </div>
          </div>
        </Card>
      </div>
      
      <div class="result-section">
        <Card title="解码结果" class="result-card">
          <template #extra>
            <Button type="primary" @click="copyResult" :disabled="!result">
              <CopyOutlined />
              复制结果
            </Button>
          </template>

          <div v-if="!result" class="empty-result">
            <SearchOutlined class="empty-icon" />
            <p>请上传二维码图片进行解码</p>
          </div>
          
          <div v-else class="result-content">
            <TextArea
              v-model:value="result"
              :rows="6"
              readonly
              placeholder="解码结果将显示在这里..."
            />
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Card, Button, Input, message } from 'ant-design-vue'
import { QrcodeOutlined, CloudUploadOutlined, CopyOutlined, DeleteOutlined, QuestionCircleOutlined, FileImageOutlined, FolderOpenOutlined, SearchOutlined } from '@ant-design/icons-vue'

const { TextArea } = Input

const result = ref('')

const selectFile = () => {
  // TODO: 实现文件选择功能
  message.info('文件选择功能将在第三周实现')
}

const pasteFromClipboard = async () => {
  try {
    // 使用浏览器的剪贴板API
    if (navigator.clipboard && navigator.clipboard.readText) {
      const text = await navigator.clipboard.readText()
      if (text) {
        result.value = text
        message.success('从剪贴板读取文本成功')
      } else {
        message.warning('剪贴板中没有文本内容')
      }
    } else {
      message.warning('当前浏览器不支持剪贴板读取功能')
    }
  } catch (error) {
    message.error('读取剪贴板失败，请检查浏览器权限')
  }
}

const copyResult = async () => {
  if (!result.value) return

  try {
    // 使用浏览器的剪贴板API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(result.value)
      message.success('复制成功')
    } else {
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea')
      textArea.value = result.value
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      message.success('复制成功')
    }
  } catch (error) {
    message.error('复制失败')
  }
}

const clearAll = () => {
  result.value = ''
  message.info('已清空')
}

const showHelp = () => {
  message.info('帮助功能将在后续版本实现')
}
</script>

<style lang="less" scoped>
.qr-decoder-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 24px;
  overflow: hidden;
}

.tool-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.tool-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tool-icon {
  font-size: 32px;
  color: #3b82f6;
}

.tool-info {
  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
  }
  
  p {
    margin: 4px 0 0 0;
    font-size: 14px;
    color: #64748b;
  }
}

.tool-actions {
  display: flex;
  gap: 12px;
}

.tool-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  overflow: hidden;
}

.upload-section, .result-section {
  display: flex;
  flex-direction: column;
}

.upload-card, .result-card {
  height: 100%;
  
  :deep(.ant-card-body) {
    height: calc(100% - 57px);
    display: flex;
    flex-direction: column;
  }
}

.upload-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }
}

.upload-icon {
  font-size: 48px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.upload-area h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1e293b;
}

.upload-area p {
  margin: 0 0 24px 0;
  color: #64748b;
  line-height: 1.5;
}

.upload-buttons {
  display: flex;
  gap: 12px;
}

.empty-result {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
